import os
from PIL import Image
import numpy as np
from tqdm import tqdm

# ================= SETTINGS =====================
TOEDIT_DIR = r"D:\Project\Toedit"
LOGOS_DIR = r"D:\Project\Logos"
OUTPUT_DIR = r"D:\Project\Edited"

LOGO_SCALE = 0.14        # Logo width ≈ 14% of image width
MARGIN_RATIO = 0.05      # 5% margin from right and bottom
JPEG_QUALITY = 95
LOG_TO_FILE = True
# =================================================

processed = 0
skipped = 0
errors = 0
log_messages = []


# ----------------- UTILITIES -----------------

def analyze_brightness(region):
    """Return average brightness and std deviation of a region."""
    gray = np.array(region.convert("L"))
    return np.mean(gray), np.std(gray)


def get_top_campaign_folder(path, base):
    rel_path = os.path.relpath(path, base)
    return rel_path.split(os.sep)[0]


def choose_logo_variant(base_name, brightness):
    """Choose logo tone (white/grey/black) based on brightness."""
    if brightness < 80:
        variant = "_white.png"
    elif brightness > 160:
        variant = "_black.png"
    else:
        variant = "_grey.png"
    logo_path = os.path.join(LOGOS_DIR, base_name + variant)
    return logo_path if os.path.exists(logo_path) else None


def detect_white_border(image, logo_height, margin_y):
    """
    Detect if the bottom region is mostly white (border).
    If so, move the logo upward until real image content.
    """
    img_gray = np.array(image.convert("L"))
    h = img_gray.shape[0]
    w = img_gray.shape[1]

    # Start sampling from bottom up
    step = int(logo_height * 0.2)  # move up gradually
    for offset in range(0, int(logo_height * 3), step):
        start_y = max(0, h - margin_y - logo_height - offset)
        end_y = h - margin_y - offset
        region = img_gray[start_y:end_y, int(w * 0.6):w]  # bottom-right slice

        if region.size == 0:
            continue

        mean_brightness = np.mean(region)
        std_dev = np.std(region)

        # Border condition: very bright and very low variation
        if mean_brightness > 240 and std_dev < 5:
            continue  # still border, move up
        else:
            return offset  # found image content

    return 0  # no need to move (no border)


# ----------------- MAIN LOGIC -----------------

def add_logo(image_path, base_name, output_path):
    global processed, errors
    try:
        image = Image.open(image_path).convert("RGBA")

        # Initial logo placement
        logo_width = int(image.width * LOGO_SCALE)
        margin_x = int(image.width * MARGIN_RATIO)
        margin_y = int(image.height * MARGIN_RATIO)
        pos_x = image.width - logo_width - margin_x

        # Sample bottom-right area brightness for tone selection
        area = image.crop((pos_x, image.height - logo_width - margin_y, image.width - margin_x, image.height - margin_y))
        brightness, _ = analyze_brightness(area)

        # Choose logo variant
        logo_path = choose_logo_variant(base_name, brightness)
        if not logo_path:
            raise FileNotFoundError(f"No matching logo variant for {base_name}")

        logo = Image.open(logo_path).convert("RGBA")

        # Resize logo
        logo_ratio = logo_width / logo.width
        logo_height = int(logo.height * logo_ratio)
        logo = logo.resize((logo_width, logo_height), Image.LANCZOS)

        # Detect white border & adjust position
        border_offset = detect_white_border(image, logo_height, margin_y)
        pos_y = image.height - logo_height - margin_y - border_offset

        # Paste logo
        image.paste(logo, (pos_x, pos_y), logo)

        # Save output
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        ext = os.path.splitext(image_path)[1].lower()
        if ext in [".jpg", ".jpeg"]:
            image.convert("RGB").save(output_path, "JPEG", quality=JPEG_QUALITY)
        else:
            image.save(output_path)
        processed += 1

    except Exception as e:
        errors += 1
        log_messages.append(f"❌ Error processing {image_path}: {e}")


def process_all():
    global skipped
    image_files = []
    for root, _, files in os.walk(TOEDIT_DIR):
        for file in files:
            if file.lower().endswith((".jpg", ".jpeg", ".png")):
                image_files.append(os.path.join(root, file))

    for image_path in tqdm(image_files, desc="📸 Processing images", unit="img"):
        campaign = get_top_campaign_folder(os.path.dirname(image_path), TOEDIT_DIR)
        base_logo_name = campaign

        # Check if logo variants exist
        variants_exist = any(os.path.exists(os.path.join(LOGOS_DIR, f"{base_logo_name}_{v}.png"))
                             for v in ["white", "grey", "black"])
        if not variants_exist:
            skipped += 1
            log_messages.append(f"⚠️ Skipped {image_path} (no logo variants for '{campaign}')")
            continue

        rel_path = os.path.relpath(os.path.dirname(image_path), TOEDIT_DIR)
        output_path = os.path.join(OUTPUT_DIR, rel_path, os.path.basename(image_path))
        add_logo(image_path, base_logo_name, output_path)

    print("\n🎯 PROCESS COMPLETE")
    print(f"✅ Processed: {processed}")
    print(f"⚠️ Skipped (no logo variants): {skipped}")
    print(f"❌ Errors: {errors}")
    print(f"📁 Output saved to: {OUTPUT_DIR}")

    if LOG_TO_FILE:
        with open("log.txt", "w", encoding="utf-8") as f:
            f.write("=== AUTO BRANDING REPORT ===\n")
            f.write(f"Processed: {processed}\nSkipped: {skipped}\nErrors: {errors}\n\n")
            for msg in log_messages:
                f.write(msg + "\n")
        print("📝 Log saved as log.txt")


if __name__ == "__main__":
    print("🚀 Starting smart border-aware branding automation...\n")
    process_all()
    print("\n✅ All campaign images processed successfully!")
